# Configuration for prompt optimization
max_iterations: 30
checkpoint_interval: 10
log_level: "INFO"

# LLM configuration
llm:
  primary_model: "qwen3-32b-fp8"
  api_base: "http://localhost:1234/v1"
  api_key: "your_api_key_here"
  temperature: 0.9
  top_p: 0.95
  max_tokens: 2048

# Prompt configuration
prompt:
  system_message: |
    You are an expert prompt engineer. Your task is to revise an existing prompt designed for large language models (LLMs), without being explicitly told what the task is.

    Your improvements should:

    * Infer the intended task and expected output format based on the structure and language of the original prompt.
    * Clarify vague instructions, eliminate ambiguity, and improve overall interpretability for the LLM.
    * Strengthen alignment between the prompt and the desired task outcome, ensuring more consistent and accurate responses.
    * Improve robustness against edge cases or unclear input phrasing.
    * If helpful, include formatting instructions, boundary conditions, or illustrative examples that reinforce the LLM's expected behavior.
    * Avoid adding unnecessary verbosity or assumptions not grounded in the original prompt.

    You will receive a prompt that uses the following structure:

    ```python
    prompt.format(input_text=some_text)
    ```

    The revised prompt should maintain the same input interface but be more effective, reliable, and production-ready for LLM use.

    Return only the improved prompt text. Do not include explanations or additional comments. Your output should be a clean, high-quality replacement that enhances clarity, consistency, and LLM performance.

  num_top_programs: 8
  use_template_stochasticity: true

# Database configuration
database:
  population_size: 40
  archive_size: 20
  num_islands: 3
  elite_selection_ratio: 0.25
  exploitation_ratio: 0.65

# Evaluator configuration
evaluator:
  timeout: 45  
  use_llm_feedback: true

# Evolution settings
diff_based_evolution: true
allow_full_rewrites: true
diversity_threshold: 0.1
