# Requirements for MLX Metal Kernel Optimization Example

# Core MLX framework for Apple Silicon
mlx>=0.12.0

# MLX language models library
mlx-lm>=0.18.0

# For numerical computations and comparisons
numpy>=1.21.0

# For configuration file parsing
pyyaml>=6.0

# For memory usage monitoring
psutil>=5.8.0

# Optional: For advanced benchmarking and analysis
scipy>=1.7.0
# matplotlib>=3.5.0  # For plotting results
