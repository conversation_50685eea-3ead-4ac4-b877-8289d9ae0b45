# Configuration for R Robust Regression Evolution

# General settings
max_iterations: 100
checkpoint_interval: 10
log_level: "INFO"

# LLM configuration
llm:
  primary_model: "gemini-2.5-flash-lite-preview-06-17"
  primary_model_weight: 0.8
  secondary_model: "gemini-2.5-flash"
  secondary_model_weight: 0.2
  api_base: "https://generativelanguage.googleapis.com/v1beta/openai/"
  
  temperature: 0.8  # Higher temperature for more creative statistical approaches
  max_tokens: 4096
  
  # Custom system message for R statistical programming
  system_message: |
    You are an expert statistician and R programmer specializing in robust regression methods.
    Focus on improving the algorithm's ability to handle outliers while maintaining accuracy.
    Consider techniques like Huber regression, RANSAC, M-estimators, and other robust methods.
    Ensure the R code is efficient and follows best practices.

# Prompt configuration
prompt:
  num_top_programs: 3
  num_diverse_programs: 2
  
  # Include artifacts to show R errors and performance metrics
  include_artifacts: true
  max_artifact_bytes: 4096

# Database configuration
database:
  population_size: 100
  num_islands: 3
  
  # Feature dimensions for robust regression
  feature_dimensions:
    - "score"           # Overall performance
    - "outlier_robustness"  # Robustness to outliers
    - "execution_time"  # Computational efficiency
  feature_bins: 5

# Evaluator configuration
evaluator:
  timeout: 30  # R scripts can be slow
  parallel_evaluations: 2  # Limit parallel R processes
  
  # Use cascade evaluation to quickly filter out broken implementations
  cascade_evaluation: true
  cascade_thresholds:
    - 0.3  # Basic correctness
    - 0.6  # Good performance
    - 0.8  # Excellent robustness