# Configuration for function minimization example
max_iterations: 20
checkpoint_interval: 1
log_level: "INFO"

# LLM configuration
llm:
  primary_model: "gemini-2.0-flash"
  primary_model_weight: 0.6
  secondary_model: "gemini-2.5-flash-preview-05-20"
  secondary_model_weight: 0.4
  api_base: "https://generativelanguage.googleapis.com/v1beta/openai/"
  api_key: YOUR_API_KEY
  temperature: 0.7
  top_p: 0.95
  max_tokens: 4096

# Prompt configuration
prompt:
  system_message: |
    You are an expert programmer. Your task is to implement an algorithm in Python to pass all the test cases. The problem is as follows:

    A string of lowercase letters is called alphabetical if some of the letters can be deleted so that the only letters that remain are the letters from a to z in order. Given a string s, determine the minimum number of letters to add anywhere in the string to make it alphabetical.

    Input:
    Each input will consist of a single test case. Note that your program may be run multiple times on different inputs. The only line of input contains a string s (1 ≤ |s| ≤ 50) which contains only lowercase letters.
    Output:
    Output a single integer, which is the smallest number of letters needed to add to s to make it alphabetical.

    Sample Input 1:
    xyzabcdefghijklmnopqrstuvw
    Sample Output 1:
    3

    Sample Input 2:
    aiemckgobjfndlhp
    Sample Output 2:
    20

    Your program should always read/write to STDIN/STDOUT. For example, to handle integer input, use the following format:
    ```
    import sys
    for line in sys.stdin:
        data = int(line)
    ```
    Use print() for output. For example:
    ```
    print("Hello, World!")
    ```
  num_top_programs: 3
  use_template_stochasticity: true

# Database configuration
database:
  population_size: 50
  archive_size: 20
  num_islands: 3
  elite_selection_ratio: 0.2
  exploitation_ratio: 0.7

# Evaluator configuration
evaluator:
  timeout: 60
  cascade_evaluation: false
  cascade_thresholds: [1.0]
  parallel_evaluations: 4
  use_llm_feedback: false

# Evolution settings
diff_based_evolution: true
allow_full_rewrites: false
